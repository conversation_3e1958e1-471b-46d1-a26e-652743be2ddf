# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/Isaac<PERSON>ab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Configuration for the dexterous hand from Shadow Robot.

The following configurations are available:

* :obj:`ROHAND_RIGHT_CFG`: Shadow Hand with implicit actuator model.

Reference:

* https://www.shadowrobot.com/dexterous-hand-series/

"""


import isaaclab.sim as sim_utils
from isaaclab.actuators.actuator_cfg import ImplicitActuatorCfg
from isaaclab.assets.articulation import ArticulationCfg

##
# Configuration
##

ROHAND_RIGHT_CFG = ArticulationCfg(
    spawn=sim_utils.UsdFileCfg(
        usd_path="/home/<USER>/IsaacLab/usd_files/wheelbot-righthand-change.usd",
        activate_contact_sensors=False,
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            disable_gravity=True,
            retain_accelerations=True,
            max_depenetration_velocity=1000.0,
        ),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=True,
            solver_position_iteration_count=8,
            solver_velocity_iteration_count=0,
            sleep_threshold=0.005,
            stabilization_threshold=0.0005,
        ),
        # collision_props=sim_utils.CollisionPropertiesCfg(contact_offset=0.005, rest_offset=0.0),
        joint_drive_props=sim_utils.JointDrivePropertiesCfg(drive_type="force"),
        fixed_tendons_props=sim_utils.FixedTendonPropertiesCfg(limit_stiffness=30.0, damping=0.1),
    ),
    init_state=ArticulationCfg.InitialStateCfg(
        pos=(0.0, 0.0, 0.5),
        rot=(1.0, 0.0, 0.0, 0.0),
        joint_pos={".*_slider_link": 0.0,
                   "righthand_th_root_link": 0.0},
    ),
    actuators={
        "fingers": ImplicitActuatorCfg(
            joint_names_expr=[".*_slider_link", "righthand_th_root_link"],
            effort_limit_sim={
                "righthand_if_slider_link": 50.0,
                "righthand_mf_slider_link": 50.0,
                "righthand_rf_slider_link": 50.0,
                "righthand_lf_slider_link": 50.0,
                "righthand_th_slider_link": 50.0,
                "righthand_th_root_link": 50.0,
            },
            stiffness={
                "righthand_if_slider_link": 10.0,
                "righthand_mf_slider_link": 10.0,
                "righthand_rf_slider_link": 10.0,
                "righthand_lf_slider_link": 10.0,
                "righthand_th_slider_link": 10.0,
                "righthand_th_root_link": 10.0,
            },
            damping={
                "righthand_if_slider_link": 100.0,
                "righthand_mf_slider_link": 100.0,
                "righthand_rf_slider_link": 100.0,
                "righthand_lf_slider_link": 100.0,
                "righthand_th_slider_link": 100.0,
                "righthand_th_root_link": 100.0,
            },
        ),
    },
    soft_joint_pos_limit_factor=1.0,
)
"""Configuration of Shadow Hand robot."""
