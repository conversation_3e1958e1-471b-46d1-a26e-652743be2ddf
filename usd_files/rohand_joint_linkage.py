from omni.kit.scripting import BehaviorScript
from pxr import Sdf
import math
import omni.usd
import traceback
import asyncio


##
# Hand Joint Linkage with Joint Path Finding
##

MAX_ANGLES_URDF = 4
MAX_ANGLES_URDF_THUMB = 3
MAX_FINGERS = 6
NUM_FINGERS = 5
EXTRA_MOTORS = 1

THUMB_ID = 0
INDEX_FINGER_ID = 1
MIDDLE_FINGER_ID = 2
RING_FINGER_ID = 3
LITTLE_FINGER_ID = 4
THUMB_ROOT_ID = 5
JOINTS_NAME = [
    ['righthand_th_proximal_link', 'righthand_th_slider_link', 'righthand_th_connecting_link', 'righthand_th_distal_link'],
    ['righthand_if_slider_link', 'righthand_if_slider_abpart_link', 'righthand_if_proximal_link', 'righthand_if_distal_link', 'righthand_if_connecting_link'],
    ['righthand_mf_slider_link', 'righthand_mf_slider_abpart_link', 'righthand_mf_proximal_link', 'righthand_mf_distal_link', 'righthand_mf_connecting_link'],
    ['righthand_rf_slider_link', 'righthand_rf_slider_abpart_link', 'righthand_rf_proximal_link', 'righthand_rf_distal_link', 'righthand_rf_connecting_link'],
    ['righthand_lf_slider_link', 'righthand_lf_slider_abpart_link', 'righthand_lf_proximal_link', 'righthand_lf_distal_link', 'righthand_lf_connecting_link'],
    ['righthand_th_root_link']
]

PI = 3.141592653589793
HALF_PI = 1.570796326794896
ONE_HALF_PI = 4.71238898038469


POS_NAGATIVE_CDF = -1.466


# 辅助函数
def METER_TO_MILLIMETER(value):
    return value * 1000

def CLAMP(value, min_value, max_value):
    return max(min(value, max_value), min_value)

def ANGLE_COS(L_a, L_b, L_c):
    return math.acos((L_b * L_b + L_c * L_c - L_a * L_a) / (2 * L_b * L_c))

def ANGLE_SIN(angle_a, L_a, L_b):
    return math.asin((L_b * math.sin(angle_a)) / L_a)

def LENGTH_COS(angle, L_b, L_c):
    return math.sqrt(L_b * L_b + L_c * L_c - 2 * L_b * L_c * math.cos(angle))

def LENGTH_SIN(angle_a, L_a, angle_b):
    return (math.sin(angle_b) * L_a) / math.sin(angle_a)


# Parameters in calibration:
ThumbParams = [
    # L_BP0, L_OP,  L_AB,  L_OA,  ∠OAR, L_CT0, L_OT,  ∠EOQ,  L_OE, L_CD,  L_ED,  ∠DEF,  L_EF,    XE, L_DF,   ∠EFD, L_CG
    11.48, 0.48, 10.98, 10.00, 0.5983, 43.78, 2.08, 0.2155, 52.52, 9.90, 11.30, 1.2976, 27.54, 51.30, 26.8, 0.4184, 13.31  # thumb
]

FingerParams = [
    # X_A0, L_AP,  L_AC,  L_OC,  ∠BOQ,  ∠COD,  ∠EDF,  L_OD,  L_CD, L_OB, L_DE,  L_BE,  L_DF
    [12.70, 8.60, 19.63, 14.25, 0.5353, 0.7624, 0.8496, 35.92, 27.44, 8.63, 7.69, 34.14, 50.23],  # index_finger
    [13.17, 8.60, 19.63, 14.31, 0.5353, 0.7763, 0.8496, 41.01, 32.39, 8.63, 7.69, 39.48, 50.23],  # middle_finger
    [13.00, 8.60, 19.63, 14.25, 0.5353, 0.7624, 0.8496, 35.92, 27.44, 8.63, 7.69, 34.14, 50.23],  # ring_finger
    [13.15, 8.60, 19.63, 14.27, 0.5353, 0.7502, 0.8141, 31.93, 23.59, 8.63, 7.53, 30.85, 35.53]   # little_finger
]

# Offset angle to the initial state in URDF, the output angle needs to minus it.
OffsetAngleForURDF_Thumb = [
    # slider,          #connecting,        #distal,
    0.4243102998823798, 2.8587571556405598, 1.5319419424521146
]

OffsetAngleForURDF = [
    # abpart,            slider,           distal,             connecting,
    [2.932191219049254, 2.8069436083614603, 2.5070833024292147, 2.0524510416836774],
    [2.9149674382495734, 2.7765308802396014, 2.5671093077696816, 2.050822356942187],
    [2.92349523247357, 2.7806045952072487, 2.4744162317989837, 2.0228874728582893],
    [2.91807750665145, 2.7543096279771366, 2.4606460165701547, 1.9656409021066734]
]


#
# Calculate the angle of thumb with the offset to reference.
def THUMB_OffsetToAngle(offset_to_ref):
    pos = METER_TO_MILLIMETER(offset_to_ref)

    # Initial the array.
    # Thumb has only 3 angles in URDF
    Angle_Thumb = [0.0] * MAX_ANGLES_URDF_THUMB
    L_BP0 = ThumbParams[0]
    L_OP = ThumbParams[1]
    L_AB = ThumbParams[2]
    L_OA = ThumbParams[3]
    Angle_OAR = ThumbParams[4]
    L_CT0 = ThumbParams[5]
    L_OT = ThumbParams[6]
    Angle_EOQ = ThumbParams[7]
    L_OE = ThumbParams[8]
    L_CD = ThumbParams[9]
    L_ED = ThumbParams[10]
    Angle_DEF = ThumbParams[11]
    L_EF = ThumbParams[12]
    XE = ThumbParams[13]

    L_BP = L_BP0 + pos
    L_OB = math.sqrt(L_OP * L_OP + L_BP * L_BP)
    Angle_OBP = math.atan(L_OP / L_BP)
    Angle_AOB = ANGLE_COS(L_AB, L_OA, L_OB)
    Angle_QOS = Angle_AOB - Angle_OBP - Angle_OAR

    L_CT = L_CT0 + pos
    L_OC = math.sqrt(L_CT * L_CT + L_OT * L_OT)
    Angle_OCT = math.atan(L_OT / L_CT)
    Angle_EOC = Angle_EOQ + Angle_OCT
    L_CE = LENGTH_COS(Angle_EOC, L_OE, L_OC)
    Angle_CED = ANGLE_COS(L_CD, L_CE, L_ED)
    Angle_CEF = Angle_CED + Angle_DEF
    L_CF = LENGTH_COS(Angle_CEF, L_CE, L_EF)
    Angle_ECF = ANGLE_COS(L_EF, L_CE, L_CF)
    Angle_ECU = HALF_PI - math.atan((XE - L_CT) / L_CE)
    Angle_DCT = ANGLE_COS(L_ED, L_CD, L_CE) + (HALF_PI - Angle_ECU + Angle_ECF) + HALF_PI

    L_OD = LENGTH_COS(Angle_DCT - Angle_OCT, L_OC, L_CD)
    Angle_OED = ANGLE_COS(L_OD, L_OE, L_ED)

    Angle_Thumb[0] = Angle_QOS - OffsetAngleForURDF_Thumb[0]
    Angle_Thumb[1] = Angle_DCT - OffsetAngleForURDF_Thumb[1]
    Angle_Thumb[2] = OffsetAngleForURDF_Thumb[2] - Angle_OED

    return Angle_Thumb


#
# Calculate the angle of other four fingers with the offset to the reference.
def FINGER_OffsetToAngle(finger_id, offset_to_ref):
    pos = METER_TO_MILLIMETER(offset_to_ref)

    # Modify index to match the array sequence.
    finger_id -= 1

    # Initial the array.
    Angle_Finger = [[0] * MAX_ANGLES_URDF for _ in range(MAX_ANGLES_URDF)]

    X_A0 = FingerParams[finger_id][0]
    L_AP = FingerParams[finger_id][1]
    L_AC = FingerParams[finger_id][2]
    L_OC = FingerParams[finger_id][3]
    Angle_BOQ = FingerParams[finger_id][4]
    Angle_COD = FingerParams[finger_id][5]
    Angle_EDF = FingerParams[finger_id][6]
    L_OD = FingerParams[finger_id][7]
    L_OB = FingerParams[finger_id][9]
    L_DE = FingerParams[finger_id][10]
    L_BE = FingerParams[finger_id][11]

    L_OP = X_A0 + pos
    L_OA = math.sqrt(L_OP * L_OP + L_AP * L_AP)
    Angle_AOP = ANGLE_COS(L_AP, L_OA, L_OP)
    Angle_OAP = HALF_PI - Angle_AOP
    Angle_CAO = ANGLE_COS(L_OC, L_AC, L_OA)
    Angle_CAR = ONE_HALF_PI - Angle_OAP - Angle_CAO  # Angle of slider_abpart

    Angle_COA = ANGLE_COS(L_AC, L_OC, L_OA)
    Angle_COP = Angle_AOP + Angle_COA
    Angle_DOP = Angle_COD + Angle_COP                # Angle of slider link

    Angle_DOQ = PI - Angle_DOP
    Angle_DOB = Angle_BOQ + Angle_DOQ
    L_BD = LENGTH_COS(Angle_DOB, L_OB, L_OD)
    Angle_BDO = ANGLE_COS(L_OB, L_BD, L_OD)
    Angle_BDE = ANGLE_COS(L_BE, L_DE, L_BD)
    Angle_EDO = Angle_BDE - Angle_BDO
    L_OE = LENGTH_COS(Angle_BDE, L_DE, L_OD)
    Angle_EBO = ANGLE_COS(L_OE, L_BE, L_OB)         # Angle of connecting link

    Angle_FDO = Angle_EDF + Angle_EDO               # Angle of distal link

    Angle_Finger[finger_id][0] = Angle_CAR - OffsetAngleForURDF[finger_id][0]  # slider_abpart
    Angle_Finger[finger_id][1] = Angle_DOP - OffsetAngleForURDF[finger_id][1]  # slider
    Angle_Finger[finger_id][2] = Angle_FDO - OffsetAngleForURDF[finger_id][2]  # distal
    Angle_Finger[finger_id][3] = Angle_EBO - OffsetAngleForURDF[finger_id][3]  # connecting

    return Angle_Finger[finger_id]


#
# Calculate the angle with the position
def HAND_FingerPosToAngle(finger_id, pos):

    # Initial the array.
    angles = [0] * MAX_ANGLES_URDF

    if finger_id >= NUM_FINGERS + EXTRA_MOTORS:
        # Invalid id
        return
    elif finger_id >= NUM_FINGERS:
        # The input value of thumb root in urdf is the value of slider position in joint_tate_publisher_gui
        angles = pos

    else:

        if finger_id == 0:
            angles = THUMB_OffsetToAngle(pos)
        else:
            angles = FINGER_OffsetToAngle(finger_id, pos)

    return angles


#
# Behavior script for hand joint linkage
class HandJointLinkage(BehaviorScript):

    def on_init(self):
        try:
            # 调试标志 - 设置为 True 启用调试日志，False 禁用
            self.debug_flag = True

            if self.debug_flag:
                print("=== HandJointLinkage: on_init called ===")

            # 初始化定时器相关变量
            self._timer_task = None
            self._is_running = False
            self._update_counter = 0

        except Exception as e:
            error_msg = f"HandJointLinkage on_init error: {e}\n{traceback.format_exc()}"
            print(error_msg)

    def on_destroy(self):
        try:
            if self.debug_flag:
                print("=== HandJointLinkage: on_destroy called ===")
            self._stop_joint_linkage_timer()
        except Exception as e:
            error_msg = f"HandJointLinkage on_destroy error: {e}\n{traceback.format_exc()}"
            print(error_msg)

    def on_play(self):
        try:
            if self.debug_flag:
                print("=== HandJointLinkage: on_play called ===")
            self._start_joint_linkage_timer()
        except Exception as e:
            error_msg = f"HandJointLinkage on_play error: {e}\n{traceback.format_exc()}"
            print(error_msg)

    def on_pause(self):
        try:
            if self.debug_flag:
                print("=== HandJointLinkage: on_pause called ===")
            self._stop_joint_linkage_timer()
        except Exception as e:
            error_msg = f"HandJointLinkage on_pause error: {e}\n{traceback.format_exc()}"
            print(error_msg)

    def on_stop(self):
        try:
            if self.debug_flag:
                print("=== HandJointLinkage: on_stop called ===")
            self._stop_joint_linkage_timer()
        except Exception as e:
            error_msg = f"HandJointLinkage on_stop error: {e}\n{traceback.format_exc()}"
            print(error_msg)

    def _start_joint_linkage_timer(self):
        """启动手部关节联动定时器"""
        try:
            if not self._is_running:
                self._is_running = True
                self._update_counter = 0
                import asyncio
                self._timer_task = asyncio.ensure_future(self._joint_linkage_loop())
                if self.debug_flag:
                    print("HandJointLinkage: Joint linkage timer started")
        except Exception as e:
            error_msg = f"HandJointLinkage _start_joint_linkage_timer error: {e}\n{traceback.format_exc()}"
            print(error_msg)

    def _stop_joint_linkage_timer(self):
        """停止手部关节联动定时器"""
        try:
            self._is_running = False
            if self._timer_task:
                self._timer_task.cancel()
                self._timer_task = None
                if self.debug_flag:
                    print("HandJointLinkage: Joint linkage timer stopped")
        except Exception as e:
            error_msg = f"HandJointLinkage _stop_joint_linkage_timer error: {e}\n{traceback.format_exc()}"
            print(error_msg)

    async def _joint_linkage_loop(self):
        """手部关节联动主循环 - 替代 on_update 功能"""
        try:
            import omni.kit.app

            # 更新频率控制参数
            self._update_frequency_hz = 60  # 设置更新频率为30Hz（可调整：10, 20, 30, 60）
            self._frame_skip = max(1, int(60 / self._update_frequency_hz))  # 计算需要跳过的帧数

            if self.debug_flag:
                print(f"HandJointLinkage: Update frequency set to {self._update_frequency_hz}Hz (every {self._frame_skip} frames)")

            while self._is_running:
                self._update_counter += 1

                # 前5次更新显示启动信息
                if self.debug_flag and self._update_counter <= 5:
                    print(f"HandJointLinkage: Processing frame #{self._update_counter}")

                # 每60帧（约1秒）显示一次状态
                elif self.debug_flag and self._update_counter % 60 == 0:
                    print(f"HandJointLinkage: Active - frame #{self._update_counter} (effective updates: {self._update_counter // self._frame_skip})")

                # 只在指定的帧执行手部关节联动计算
                if self._update_counter % self._frame_skip == 0:
                    self._update_hand_joint_linkage()

                # 等待下一帧
                await omni.kit.app.get_app().next_update_async()

        except asyncio.CancelledError:
            if self.debug_flag:
                print("HandJointLinkage: Joint linkage loop cancelled")
        except Exception as e:
            error_msg = f"HandJointLinkage _joint_linkage_loop error: {e}\n{traceback.format_exc()}"
            print(error_msg)

    def _update_hand_joint_linkage(self):
        """执行手部关节联动计算"""
        try:
            # 获取当前stage
            stage = omni.usd.get_context().get_stage()
            if not stage:
                return
            
            # 获取当前脚本所附加的prim（joints Scope）
            if not hasattr(self, 'prim') or not self.prim:
                if self.debug_flag and self._update_counter % 300 == 0:
                    print("HandJointLinkage: Cannot get current prim (joints scope)")
                return
                
            joints_scope_path = self.prim.GetPath()
            
            # 为每个手指处理关节联动
            for finger_id in range(NUM_FINGERS + EXTRA_MOTORS):  # 包含5个手指 + 1个拇指根部
                try:
                    if finger_id == THUMB_ROOT_ID:
                        # 处理拇指根部（如果需要的话）
                        continue
                        
                    # 获取当前手指的关节名称列表
                    joint_names = JOINTS_NAME[finger_id]
                    
                    # 找到slider关节（第二个关节通常是slider）
                    slider_joint_name = None
                    slider_joint_index = -1
                    
                    if finger_id == THUMB_ID:
                        # 拇指的slider是第二个关节 'th_slider_link'
                        slider_joint_name = joint_names[1]  # 'th_slider_link'
                        slider_joint_index = 1
                    else:
                        # 其他手指的slider是第一个关节 '*_slider_link'
                        slider_joint_name = joint_names[0]  # '*f_slider_link'
                        slider_joint_index = 0
                    
                    # 使用新的关节查找方法
                    slider_joint_path_str = self.find_joint_path_by_name(slider_joint_name)
                    if not slider_joint_path_str:
                        if self.debug_flag and self._update_counter % 300 == 0:  # 每5秒报告一次
                            print(f"HandJointLinkage: Slider joint not found: {slider_joint_name}")
                        continue

                    slider_joint_path = Sdf.Path(slider_joint_path_str)
                    slider_prim = stage.GetPrimAtPath(slider_joint_path)
                    
                    # 获取slider关节的当前位移 - 修正的方法
                    current_position = self._get_joint_position(slider_prim, is_prismatic=True)
                    if current_position is None:
                        continue
                    
                    # 限制位移范围
                    if finger_id == THUMB_ID:
                        # 拇指位移范围：-0.003~0.008
                        clamped_position = CLAMP(current_position, -0.003, 0.008)
                    else:
                        # 其他手指位移范围：-0.003~0.016
                        clamped_position = CLAMP(current_position, -0.003, 0.016)
                    
                    # 调用HAND_FingerPosToAngle计算目标角度
                    target_angles = HAND_FingerPosToAngle(finger_id, clamped_position)
                    
                    if not target_angles:
                        continue
                    
                    # 设置除slider外的其他关节到目标角度
                    for joint_idx, joint_name in enumerate(joint_names):
                        # 跳过slider关节本身
                        if joint_idx == slider_joint_index:
                            continue
                        
                        # 使用新的关节查找方法
                        joint_path_str = self.find_joint_path_by_name(joint_name)
                        if not joint_path_str:
                            continue

                        joint_path = Sdf.Path(joint_path_str)
                        joint_prim = stage.GetPrimAtPath(joint_path)
                        
                        # 计算目标角度索引
                        angle_index = self._get_angle_index_for_joint(finger_id, joint_idx, slider_joint_index)
                        
                        if angle_index >= 0 and isinstance(target_angles, list) and angle_index < len(target_angles):
                            target_angle = target_angles[angle_index]
                            
                            # 设置关节目标角度 - 修正的方法
                            self._set_joint_position(joint_prim, target_angle, is_prismatic=False)
                    
                    # 调试输出（限制频率）
                    if self.debug_flag and self._update_counter % 180 == 0:  # 每3秒输出一次信息
                        print(f"HandJointLinkage: Finger {finger_id}, Slider pos: {clamped_position:.6f}, Target angles: {target_angles}")
                        
                except Exception as e:
                    if not hasattr(self, f'_finger_error_count_{finger_id}'):
                        setattr(self, f'_finger_error_count_{finger_id}', 0)
                    
                    error_count = getattr(self, f'_finger_error_count_{finger_id}')
                    if error_count < 3:  # 每个手指只报告前3次错误
                        setattr(self, f'_finger_error_count_{finger_id}', error_count + 1)
                        print(f"HandJointLinkage: Error processing finger {finger_id}: {e}")
            
            # 调试信息：显示joints scope路径和关节状态
            if self.debug_flag and self._update_counter == 1:
                print(f"HandJointLinkage: Using joints scope path: {joints_scope_path}")
                # 在第一次更新时检查所有slider关节的属性
                self.debug_all_slider_joints()
            
        except Exception as e:
            # 只记录前几次错误，避免日志刷屏
            if not hasattr(self, '_linkage_error_count'):
                self._linkage_error_count = 0

            self._linkage_error_count += 1
            if self._linkage_error_count <= 5:
                error_msg = f"HandJointLinkage _update_hand_joint_linkage error #{self._linkage_error_count}: {e}\n{traceback.format_exc()}"
                print(error_msg)

    def _get_joint_position(self, joint_prim, is_prismatic=True):
        """
        获取关节的当前位置

        Args:
            joint_prim: 关节的USD Prim
            is_prismatic: 是否为移动关节（True）或旋转关节（False）

        Returns:
            当前位置值，如果失败返回None
        """
        try:
            # 首先列出所有可能的位置属性名
            position_attr_names = []

            if is_prismatic:
                position_attr_names = [
                    "drive:linear:physics:targetPosition",
                    "drive:linear:physics:position",
                    "physics:position",
                    "physics:linearPosition",
                    "state:position",
                    "state:pos",
                    "position",
                    "targetPosition",
                    "currentPosition"
                ]
            else:
                position_attr_names = [
                    "drive:angular:physics:targetPosition",
                    "drive:angular:physics:position",
                    "physics:position",
                    "physics:angularPosition",
                    "state:position",
                    "state:pos",
                    "position",
                    "targetPosition",
                    "currentPosition"
                ]

            # 尝试从已知的属性名获取
            for attr_name in position_attr_names:
                attr = joint_prim.GetAttribute(attr_name)
                if attr and attr.HasValue():
                    value = attr.Get()
                    if isinstance(value, (int, float)):
                        if self.debug_flag and self._update_counter % 600 == 0:  # 减少日志频率
                            print(f"HandJointLinkage: Found position in {attr_name}: {value}")
                        return value

            # 如果上述方法都失败，遍历所有属性查找
            for attr in joint_prim.GetAttributes():
                attr_name = attr.GetName()
                if ("position" in attr_name.lower() or "pos" in attr_name.lower()) and attr.HasValue():
                    value = attr.Get()
                    if isinstance(value, (int, float)):
                        if self.debug_flag and self._update_counter % 600 == 0:
                            print(f"HandJointLinkage: Found position in attribute: {attr_name} = {value}")
                        return value

            # 如果都没有找到，打印调试信息并返回默认值
            if self.debug_flag and self._update_counter % 600 == 0:  # 减少日志频率
                print(f"HandJointLinkage: No position attribute found for joint: {joint_prim.GetPath()}")
                print("  Available attributes:")
                for attr in joint_prim.GetAttributes():
                    print(f"    - {attr.GetName()}: {attr.GetTypeName()}")

            return 0.005 if is_prismatic else 0.0  # 给slider一个中间值

        except Exception as e:
            if self.debug_flag:
                print(f"HandJointLinkage _get_joint_position error: {e}")
            return None

    def _set_joint_position(self, joint_prim, target_value, is_prismatic=True):
        """设置关节的目标位置"""
        try:
            # 对于旋转关节，将弧度转换为角度
            target_value_to_set = math.degrees(target_value) if not is_prismatic else target_value
            success = False

            # 定义可能的属性名
            if is_prismatic:
                attr_names = [
                    "drive:linear:physics:targetPosition",
                    "drive:linear:physics:position",
                    "physics:position",
                    "physics:linearPosition"
                ]
            else:
                attr_names = [
                    "drive:angular:physics:targetPosition",
                    "drive:angular:physics:position",
                    "physics:position",
                    "physics:angularPosition"
                ]

            # 尝试设置已存在的属性
            for attr_name in attr_names:
                attr = joint_prim.GetAttribute(attr_name)
                if attr:
                    attr.Set(float(target_value_to_set))
                    success = True
                    if self.debug_flag and self._update_counter % 1200 == 0:  # 减少日志频率
                        print(f"HandJointLinkage: Set {attr_name} to {target_value_to_set:.4f}")
                    break

            # 如果没有找到已存在的属性，创建主要的drive属性
            if not success:
                primary_attr_name = attr_names[0]  # 使用第一个作为主要属性
                drive_attr = joint_prim.CreateAttribute(primary_attr_name, Sdf.ValueTypeNames.Float)
                if drive_attr:
                    drive_attr.Set(float(target_value_to_set))
                    success = True
                    if self.debug_flag and self._update_counter % 1200 == 0:
                        print(f"HandJointLinkage: Created and set {primary_attr_name} to {target_value_to_set:.4f}")

            # 如果仍然失败，记录错误
            if not success and self.debug_flag and self._update_counter % 600 == 0:
                joint_type = "prismatic" if is_prismatic else "revolute"
                print(f"HandJointLinkage: Failed to set {joint_type} joint {joint_prim.GetPath().name}")

        except Exception as e:
            if self.debug_flag:
                print(f"HandJointLinkage _set_joint_position error: {e}")

    def find_joint_path_by_name(self, joint_name):
        """
        通过关节名统一查找该关节的路径

        Args:
            joint_name (str): 关节名称，例如 'righthand_th_proximal_link'

        Returns:
            str: 关节的完整路径，如果未找到返回None
        """
        try:
            # 获取当前stage
            stage = omni.usd.get_context().get_stage()
            if not stage:
                if self.debug_flag:
                    print(f"HandJointLinkage: Cannot get stage for joint search: {joint_name}")
                return None

            # 获取当前脚本所附加的prim（joints Scope）
            if not hasattr(self, 'prim') or not self.prim:
                if self.debug_flag:
                    print(f"HandJointLinkage: Cannot get current prim for joint search: {joint_name}")
                return None

            joints_scope_path = self.prim.GetPath()

            # 方法1：直接在joints scope下查找
            direct_joint_path = joints_scope_path.AppendChild(joint_name)
            direct_prim = stage.GetPrimAtPath(direct_joint_path)
            if direct_prim.IsValid():
                return str(direct_joint_path)

            # 方法2：在joints scope的子目录中递归查找
            joints_scope_prim = stage.GetPrimAtPath(joints_scope_path)
            if joints_scope_prim.IsValid():
                found_path = self._recursive_find_joint(stage, joints_scope_prim, joint_name)
                if found_path:
                    return found_path

            # 方法3：在整个stage中查找（作为备选方案）
            root_prim = stage.GetPseudoRoot()
            found_path = self._recursive_find_joint(stage, root_prim, joint_name)
            if found_path:
                return found_path

            if self.debug_flag:
                print(f"HandJointLinkage: Joint not found: {joint_name}")
            return None

        except Exception as e:
            if self.debug_flag:
                print(f"HandJointLinkage find_joint_path_by_name error: {e}")
            return None

    def _recursive_find_joint(self, stage, parent_prim, joint_name, max_depth=5, current_depth=0):
        """
        递归查找关节

        Args:
            stage: USD Stage
            parent_prim: 父级Prim
            joint_name: 要查找的关节名称
            max_depth: 最大搜索深度
            current_depth: 当前搜索深度

        Returns:
            str: 找到的关节路径，未找到返回None
        """
        try:
            if current_depth > max_depth:
                return None

            # 检查当前prim的所有子prim
            for child_prim in parent_prim.GetChildren():
                # 检查子prim的名称是否匹配
                if child_prim.GetName() == joint_name:
                    return str(child_prim.GetPath())

                # 递归搜索子prim的子级
                if current_depth < max_depth:
                    found_path = self._recursive_find_joint(stage, child_prim, joint_name, max_depth, current_depth + 1)
                    if found_path:
                        return found_path

            return None

        except Exception as e:
            if self.debug_flag:
                print(f"HandJointLinkage _recursive_find_joint error: {e}")
            return None

    def get_all_joint_paths(self):
        """
        获取所有已知关节的路径映射

        Returns:
            dict: 关节名称到路径的映射字典
        """
        try:
            joint_paths = {}

            # 遍历所有手指的所有关节
            for finger_id in range(len(JOINTS_NAME)):
                joint_names = JOINTS_NAME[finger_id]
                for joint_name in joint_names:
                    joint_path = self.find_joint_path_by_name(joint_name)
                    if joint_path:
                        joint_paths[joint_name] = joint_path
                    else:
                        if self.debug_flag:
                            print(f"HandJointLinkage: Could not find path for joint: {joint_name}")

            return joint_paths

        except Exception as e:
            if self.debug_flag:
                print(f"HandJointLinkage get_all_joint_paths error: {e}")
            return {}

    def find_joint_by_partial_name(self, partial_name):
        """
        通过部分关节名称查找匹配的关节

        Args:
            partial_name (str): 关节名称的一部分，例如 'th_proximal' 或 'slider'

        Returns:
            list: 匹配的关节名称和路径的列表
        """
        try:
            matches = []

            for finger_id in range(len(JOINTS_NAME)):
                joint_names = JOINTS_NAME[finger_id]
                for joint_name in joint_names:
                    if partial_name.lower() in joint_name.lower():
                        joint_path = self.find_joint_path_by_name(joint_name)
                        matches.append({
                            'name': joint_name,
                            'path': joint_path,
                            'finger_id': finger_id,
                            'found': joint_path is not None
                        })

            return matches

        except Exception as e:
            if self.debug_flag:
                print(f"HandJointLinkage find_joint_by_partial_name error: {e}")
            return []

    def debug_joint_attributes(self, joint_name):
        """调试方法：打印指定关节的所有属性"""
        try:
            joint_path = self.find_joint_path_by_name(joint_name)
            if not joint_path:
                print(f"Joint not found: {joint_name}")
                return

            stage = omni.usd.get_context().get_stage()
            joint_prim = stage.GetPrimAtPath(Sdf.Path(joint_path))

            print(f"=== Debug Joint: {joint_name} ===")
            print(f"Path: {joint_path}")
            print(f"Type: {joint_prim.GetTypeName()}")
            print(f"Valid: {joint_prim.IsValid()}")
            print("Attributes:")

            for attr in joint_prim.GetAttributes():
                attr_name = attr.GetName()
                attr_type = attr.GetTypeName()
                has_value = attr.HasValue()
                value = attr.Get() if has_value else "No Value"
                print(f"  - {attr_name}: {attr_type} = {value}")

            print("=" * 50)

        except Exception as e:
            print(f"debug_joint_attributes error: {e}")

    def debug_all_slider_joints(self):
        """调试方法：检查所有slider关节的状态"""
        try:
            print("=== Debug All Slider Joints ===")

            slider_joints = [
                'righthand_th_slider_link',
                'righthand_if_slider_link',
                'righthand_mf_slider_link',
                'righthand_rf_slider_link',
                'righthand_lf_slider_link'
            ]

            for joint_name in slider_joints:
                self.debug_joint_attributes(joint_name)

        except Exception as e:
            print(f"debug_all_slider_joints error: {e}")

    def _get_angle_index_for_joint(self, finger_id, joint_idx, slider_joint_index):
        """
        获取关节对应的角度索引

        Args:
            finger_id: 手指ID
            joint_idx: 关节在JOINTS_NAME中的索引
            slider_joint_index: slider关节的索引

        Returns:
            角度数组中对应的索引，-1表示无效
        """
        try:
            if finger_id == THUMB_ID:
                # 拇指关节映射：
                # joint_idx: 0=th_proximal_link, 1=th_slider_link(跳过), 2=th_connecting_link, 3=th_distal_link
                # angle_idx: 0=slider, 1=connecting, 2=distal
                joint_to_angle_map = {0: 0, 1: -1, 2: 1, 3: 2}  # proximal=0, slider跳过, connecting=1, distal=2
                return joint_to_angle_map.get(joint_idx, -1)
            else:
                # 其他手指关节映射：
                # joint_idx: 0=*_slider_link(跳过), 1=*_slider_abpart_link, 2=*_proximal_link, 3=*_distal_link, 4=*_connecting_link
                # angle_idx: 0=abpart, 1=slider, 2=distal, 3=connecting
                joint_to_angle_map = {0: -1, 1: 0, 2: 1, 3: 2, 4: 3}  # slider跳过, abpart=0, proximal=1, distal=2, connecting=3
                return joint_to_angle_map.get(joint_idx, -1)

        except Exception as e:
            if self.debug_flag:
                print(f"HandJointLinkage _get_angle_index_for_joint error: {e}")
            return -1
