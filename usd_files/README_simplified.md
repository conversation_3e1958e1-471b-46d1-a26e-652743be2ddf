# 简化版手部关节联动系统

## 概述

这是一个简化版的手部关节联动系统，去除了冗余的调试功能和复杂的错误处理，只保留核心功能。

## 主要功能

### 1. 关节路径查找
- `find_joint_path_by_name(joint_name)` - 通过关节名查找路径
- `get_all_joint_paths()` - 获取所有关节路径映射

### 2. 关节联动控制
- 自动根据slider关节位置计算其他关节角度
- 支持拇指和四指的不同计算模式
- 实时更新关节位置

## 文件结构

```
usd_files/
├── rohand_joint_linkage_simplified.py  # 简化版主文件
├── rohand_joint_linkage.py            # 完整版（包含调试功能）
└── README_simplified.md               # 本文档
```

## 使用方法

### 在 Omniverse 中使用

1. 将 `rohand_joint_linkage_simplified.py` 脚本附加到场景中的关节组（joints scope）
2. 脚本会自动初始化并开始关节联动

### 基本API

```python
# 查找单个关节路径
joint_path = self.find_joint_path_by_name('righthand_th_proximal_link')

# 获取所有关节路径
all_paths = self.get_all_joint_paths()
```

## 支持的关节

### 拇指 (4个关节)
- `righthand_th_proximal_link`
- `righthand_th_slider_link` (控制关节)
- `righthand_th_connecting_link`
- `righthand_th_distal_link`

### 食指 (5个关节)
- `righthand_if_slider_link` (控制关节)
- `righthand_if_slider_abpart_link`
- `righthand_if_proximal_link`
- `righthand_if_distal_link`
- `righthand_if_connecting_link`

### 中指、无名指、小指
结构与食指相同，分别以 `mf_`、`rf_`、`lf_` 开头。

### 拇指根部 (1个关节)
- `righthand_th_root_link`

## 工作原理

1. **关节查找**：使用多层策略查找关节路径
   - 直接在joints scope下查找
   - 递归搜索子目录（最大深度3层）

2. **联动计算**：
   - 监控slider关节的位移
   - 根据机械结构参数计算其他关节角度
   - 实时更新关节位置

3. **更新频率**：默认30Hz，可调整

## 配置参数

### 位移范围限制
- 拇指：-0.003 ~ 0.008 米
- 其他手指：-0.003 ~ 0.016 米

### 更新频率
- 默认：30Hz
- 可通过修改 `_update_frequency_hz` 调整

## 与完整版的区别

简化版相比完整版去除了：
- 详细的调试输出
- 测试和示例方法
- 复杂的错误处理
- 多种关节设置方法的尝试
- 冗余的辅助函数

保留了：
- 核心的关节查找功能
- 关节联动计算
- 基本的错误处理
- 必要的参数和常量

## 性能优化

1. **简化的查找算法**：减少了递归深度和尝试次数
2. **精简的错误处理**：只保留必要的异常捕获
3. **减少的调试输出**：提高运行效率
4. **优化的关节设置**：只使用最有效的方法

## 故障排除

### 常见问题

1. **关节未找到**
   - 检查关节名称是否正确
   - 确认脚本附加到正确的joints scope

2. **联动不工作**
   - 检查slider关节是否存在
   - 确认关节属性设置权限

3. **性能问题**
   - 降低更新频率
   - 检查场景复杂度

### 调试建议

如果需要详细调试信息，建议使用完整版 `rohand_joint_linkage.py`，它包含：
- 详细的日志输出
- 测试方法
- 更全面的错误处理

## 扩展功能

如需添加新功能，建议的扩展点：

1. **新的关节类型**：在 `JOINTS_NAME` 中添加
2. **自定义参数**：修改 `ThumbParams` 和 `FingerParams`
3. **不同的更新策略**：修改 `_joint_linkage_loop` 方法
4. **额外的关节查找策略**：扩展 `find_joint_path_by_name` 方法

## 版本信息

- 简化版本：基于完整版本简化而来
- 兼容性：与 Omniverse USD 场景兼容
- 依赖：pxr, omni.usd, omni.kit.scripting
